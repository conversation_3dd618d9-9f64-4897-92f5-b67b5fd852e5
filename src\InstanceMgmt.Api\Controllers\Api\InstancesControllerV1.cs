using System.Net;
using Aveva.Platform.AccountMgmt.Client.Ops.Models;
using Aveva.Platform.Authorization.Sdk;
using Aveva.Platform.Authorization.Sdk.Domain.Models;
using Aveva.Platform.Catalog.Client.V2.Ops.Models;
using Aveva.Platform.Common.Monitoring.Instrumentation.Logging;
using Aveva.Platform.InstanceMgmt.Api.Extensions;
using Aveva.Platform.InstanceMgmt.Api.Logging;
using Aveva.Platform.InstanceMgmt.Api.Models.Api.v1;
using Aveva.Platform.InstanceMgmt.Api.Models.Common.v1;
using Aveva.Platform.InstanceMgmt.Api.Validation;
using Aveva.Platform.InstanceMgmt.Application;
using Aveva.Platform.InstanceMgmt.Application.Clients.AccountMgmt;
using Aveva.Platform.InstanceMgmt.Application.Clients.Catalog;
using Aveva.Platform.InstanceMgmt.Application.Extensions;
using Aveva.Platform.InstanceMgmt.Application.Factories;
using Aveva.Platform.InstanceMgmt.Application.Services;
using Aveva.Platform.InstanceMgmt.Application.Validators;
using Aveva.Platform.InstanceMgmt.Domain.Instances;
using Aveva.Platform.InstanceMgmt.Lop.Abstractions;
using Aveva.Platform.Library.Cosmos.Abstractions.Exceptions;
using Aveva.Platform.Library.Cosmos.Abstractions.Query;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Scope = Aveva.Platform.InstanceMgmt.Application.Validators.Scope;

namespace Aveva.Platform.InstanceMgmt.Api.Controllers.Api;

/// <summary>
/// Manage service instances for your account. These endpoints support creating, retrieving, updating, and deleting service instances. *Service instances* represent deployed service resources that provide specific functionality to your applications. Use these endpoints to provision and configure the services your applications depend on.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="InstancesControllerV1"/> class.
/// </remarks>
/// <param name="repository">Repository.</param>
/// <param name="accountMgmtClient">AccountMgmt Client.</param>
/// <param name="catalogClient">Catalog Client.</param>
/// <param name="instanceApplicationFactory">Instance Application Factory.</param>
/// <param name="dependencyValidator">Service Dependency Validator.</param>
/// <param name="labelValidator">Label Validator.</param>
/// <param name="authorization">Authorization.</param>
/// <param name="availabilityService">Limits Service.</param>
/// <param name="logger">Logger.</param>
/// <param name="instanceValidator">Instance Validator.</param>
/// <param name="lifecycleOperationValidatorFactory">Lifecycle Operation Validator Factory.</param>
/// <param name="apiBehaviourOptions">Api Behaviour Options.</param>
[Route("api/v1")]
[ApiController]
[ApiExplorerSettings(GroupName = "api")]
public class InstancesControllerV1(
    IInstanceRepository repository,
    IAccountMgmtClient accountMgmtClient,
    ICatalogClient catalogClient,
    IInstanceApplicationFactory instanceApplicationFactory,
    IDependencyValidator dependencyValidator,
    ILabelValidator labelValidator,
    IAuthorizationProvider authorization,
    IInstanceAvailabilityService availabilityService,
    ILogger<InstancesControllerV1> logger,
    IInstanceValidator instanceValidator,
    ILifecycleOperationValidatorFactory lifecycleOperationValidatorFactory,
    IOptions<ApiBehaviorOptions> apiBehaviourOptions) : ControllerBase
{
    private const string QueryProblemTitle = "Cannot query for instances";
    private const string GetProblemTitle = "Cannot retrieve instance";
    private const string CreateProblemTitle = "Instance not created";
    private const string UpdateProblemTitle = "Instance not updated";
    private const string DeleteProblemTitle = "Instance not deleted";

    /// <summary>
    /// Query service instances
    /// </summary>
    /// <remarks>
    /// Queries service instances based on filter criteria such as service ID, geography, status, and more.
    /// If no filters are specified, all instances for the account will be returned.
    /// 
    /// Multiple query parameters can be combined to narrow results. When combining filters, instances must satisfy all criteria to be included in the response (logical AND behavior).
    /// 
    /// Common query scenarios:
    /// - To find all instances of a specific service: Filter by `serviceId`.
    /// - To find instances in a particular region: Filter by `geography`.
    /// - To find only active instances: Filter by `status=[Active]`.
    /// - To find instances created within a date range: Use `createdDateFrom` and `createdDateTo`.
    /// - To find instances with specific labels: Use the `labelIds` parameter with comma-separated GUIDs.
    /// - To find active instances of a specific service in a particular geography: Combine `serviceId`, `geography`, and `status=[Active]` parameters.
    /// 
    /// This operation supports two pagination methods:
    /// - Token-based pagination: Use `limit` and `continuationToken` parameters.
    /// - Offset-based pagination: Use `skip` and `count` parameters.
    /// 
    /// Results are filtered by the requesting identity's permissions. You must have read access to instances.
    /// </remarks>
    /// <returns>A collection of instances matching the query criteria.</returns>
    [HttpGet("instances", Name = "getInstances")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(InstanceCollectionResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ConnectAuthorizationResponseType("instances", "read")]
    public async Task<IActionResult> QueryAsync([FromQuery] InstanceQueryRequest? queryRequest)
    {
        if (queryRequest is null)
            return QueryBadRequest("A query request body is required.");

        var account = Request.Headers.GetAccount();
        if (string.IsNullOrWhiteSpace(account))
            return QueryBadRequest("Account Id is required.");

        var authorizationResult = await authorization.GetPolicyStatementsAsync(
            ResourcePath.Parse(AuthorizationPolicies.Resources.AnyInstance),
            ResourceAction.Read,
            CancellationToken.None).ConfigureAwait(false);

        if (!authorizationResult.IsSuccess ||
            authorizationResult.Value?.PolicyStatementResultType == PolicyStatementsResultType.None)
        {
            logger.Forbidden();
            return Forbidden(QueryProblemTitle, "query");
        }

        var filter = queryRequest.MapToQueryFilter();
        var paging = queryRequest.MapToPaging();

        filter.AccountId = account;
        if (!filter.MapAuthorization(authorizationResult))
        {
            return EmptyInstanceCollectionResponse(paging);
        }

        if (queryRequest.Category != null)
        {
            var servicesInCategory = await GetServicesInCategoryAsync(queryRequest.Category).ConfigureAwait(false);

            if (servicesInCategory.Count == 0)
            {
                logger.NoServicesInCategory(queryRequest.Category);
                return EmptyInstanceCollectionResponse(paging);
            }

            filter.ServiceIds = filter.ServiceIds == null
                ? servicesInCategory
                : filter.ServiceIds.Intersect(servicesInCategory, StringComparer.OrdinalIgnoreCase);
        }

        if (queryRequest.UserManaged != null)
        {
            var filteredServices = await GetFilteredManagedServicesAsync((bool)queryRequest.UserManaged).ConfigureAwait(false);

            if (filteredServices.Count == 0)
            {
                if (queryRequest.UserManaged == true)
                {
                    logger.NoUserManagedServices();
                }
                else
                {
                    logger.NoAccountManagedServices();
                }

                return EmptyInstanceCollectionResponse(paging);
            }

            filter.ServiceIds = filter.ServiceIds == null
                ? filteredServices
                : filter.ServiceIds.Intersect(filteredServices, StringComparer.OrdinalIgnoreCase);
        }

        try
        {
            var response = paging switch
            {
                OffsetPaging offsetPaging => InstanceCollectionResponse.MapFromOffsetPagedQueryResult(
                    await repository.QueryAsync(filter, offsetPaging).ConfigureAwait(false)),
                _ => InstanceCollectionResponse.MapFromValuePagedQueryResult(
                    await repository.QueryAsync(filter, (ValueBasedPaging)paging).ConfigureAwait(false)),
            };
            logger.QueryInstancesPerformed(response.Items.Count());
            return Ok(response);
        }
        catch (MalformedContinuationTokenException)
        {
            ModelState.AddModelError(nameof(InstanceQueryRequest.ContinuationToken), "Malformed continuation token.");
            return apiBehaviourOptions.Value.InvalidModelStateResponseFactory(ControllerContext);
        }
    }

    /// <summary>
    /// Get service instance
    /// </summary>
    /// <remarks>
    /// Gets a single service instance by its service ID and instance ID. 
    /// You must have read access to the specific instance.
    /// </remarks>
    /// <param name="serviceId">The service ID that identifies which service this instance belongs to. For a list of available service IDs, make a GET request to `/api/account/{accountId}/catalog/v2/services`.</param>
    /// <param name="id">The instance ID that uniquely identifies this specific instance. For a list of available instance IDs, make a GET request to `/api/account/{accountId}/instanceMgmt/v1/instances`.</param>
    /// <returns>The requested instance details.</returns>
    [HttpGet("services/{serviceId}/instances/{id}", Name = "getInstance")]
    [ActionName(nameof(GetAsync))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(InstanceResponse))]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ConnectAuthorizationResponseType("instances", "read")]
    public async Task<ActionResult<InstanceResponse>> GetAsync([FromRoute] string serviceId, [FromRoute] string id)
    {
        var account = Request.Headers.GetAccount();
        if (string.IsNullOrWhiteSpace(account))
            return GetBadRequest("Account Id is required.");

        if (string.IsNullOrWhiteSpace(serviceId))
            return GetBadRequest("Service Id is required.");

        if (string.IsNullOrWhiteSpace(id))
            return GetBadRequest("Instance Id is required.");

        var authorizationResult = await authorization.AuthorizeResourceAsync(
            ResourcePath.Parse(AuthorizationPolicies.Resources.Instance(serviceId, id)),
            ResourceAction.Read,
            new AuthorizationMetadata(),
            CancellationToken.None).ConfigureAwait(false);

        if (!authorizationResult.IsSuccess)
        {
            logger.Forbidden();
            return Forbidden(GetProblemTitle, "get", serviceId, id);
        }

        var queryResult = await repository.QueryAsync(new InstanceQueryFilter
        {
            AccountId = account,
            ServiceIds = [serviceId],
            InstanceId = id,
        }).ConfigureAwait(false);
        var instance = queryResult.Items.SingleOrDefault();
        if (instance == null)
        {
            logger.InstanceNotFoundWithInstanceId(account, serviceId, id);
            return InstanceNotFound(GetProblemTitle, serviceId, id);
        }

        logger.InstanceFoundWithInstanceId(account, serviceId, id);
        return Ok(instance.MapToResponse());
    }

    /// <summary>
    /// Create service instance
    /// </summary>
    /// <remarks>
    /// Creates a new service instance in your account. The service instance will be created in the specified geography with any required dependencies.
    /// 
    /// You must have create access to instances for the specified service. Only services with a trigger value of `Catalog` can have instances created through this endpoint.
    /// 
    /// The service must be supported in the requested geography. To check which geographies are supported for a service, make a GET request to `/api/account/{accountId}/catalog/v2/services/{serviceId}` and look for the `geographies` property in the response. For external services, you must select a geography that is included in this list.
    /// </remarks>
    /// <param name="serviceId">The service ID that identifies which service to create an instance of. For a list of available service IDs, make a GET request to `/api/account/{accountId}/catalog/v2/services`.</param>
    /// <param name="createRequest">The details of the service instance to create, including ID, geography, name, dependencies, and labels.</param>
    /// <returns>The newly created service instance details.</returns>
    [HttpPost("services/{serviceId}/instances", Name = "addInstance")]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(InstanceResponse))]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [BadRequestResponseType(["instanceLimitReached"], ["Entitlement limit reached"])]
    [ConnectAuthorizationResponseType("instances", "create")]
    public async Task<ActionResult<InstanceResponse>> CreateAsync([FromRoute] string serviceId, [FromBody] InstanceCreateRequest? createRequest)
    {
        if (createRequest == null)
            return CreateBadRequest("A create request body is required.");

        var accountId = Request.Headers.GetAccount();
        if (string.IsNullOrWhiteSpace(accountId))
            return CreateBadRequest("Account Id is required.");

        if (string.IsNullOrWhiteSpace(serviceId))
            return CreateBadRequest("Service Id is required.");

        var authorizationResult = await authorization.AuthorizeResourceAsync(
            ResourcePath.Parse(AuthorizationPolicies.Resources.Service(serviceId)),
            ResourceAction.Create,
            new AuthorizationMetadata(),
            CancellationToken.None).ConfigureAwait(false);

        if (!authorizationResult.IsSuccess)
        {
            logger.Forbidden();
            return Forbidden(CreateProblemTitle, ResourceAction.Create, serviceId);
        }

        var (geography, catalogService, account) = await Extensions.TaskExtensions.WhenAll(
            accountMgmtClient.GetGeographyAsync(createRequest.Geography!),
            catalogClient.GetServiceAsync(serviceId, accountId),
            accountMgmtClient.GetAccountAsync(accountId)).ConfigureAwait(false);

        if (geography == null)
        {
            logger.GeographyNotFound(createRequest.Geography!);
            return Problem(
                title: CreateProblemTitle,
                detail: $"Geography '{createRequest.Geography}' not found.",
                statusCode: (int)HttpStatusCode.BadRequest);
        }

        if (catalogService == null)
        {
            logger.CatalogServiceNotFound(serviceId);
            return CreateBadRequest($"Catalog Service '{serviceId}' not found.");
        }

        if (catalogService.Lifecycle?.Trigger != LifecycleTriggers.Catalog)
        {
            logger.InvalidInstanceCreateRequested(serviceId);
            return CreateBadRequest($"Cannot create an instance of this service '{serviceId}'.");
        }

        if (catalogService.HostingType != HostingTypes.External && (geography.Extension ?? false))
        {
            logger.InvalidInstanceCreateRequested(serviceId);
            return CreateBadRequest($"Cannot create an instance of this service '{serviceId}' in geography '{geography.Name}'.");
        }

        if (catalogService.HostingType == HostingTypes.External && !ExternalGeographySupported(catalogService, geography.Id))
        {
            logger.InvalidInstanceCreateRequested(serviceId);
            return CreateBadRequest($"Cannot create an instance of this service '{serviceId}' in geography '{geography.Name}'.");
        }

        if (account == null)
        {
            logger.AccountNotFound(accountId);

            return CreateBadRequest($"Account '{accountId}' not found.");
        }

        if (account.Status != AccountStatus.Active)
        {
            logger.AccountNotActive(accountId);
            return CreateBadRequest($"Account '{accountId}' must be Active.");
        }

        if (account.AllowedGeographies != null && !account.AllowedGeographies.Contains(geography.Id, StringComparer.OrdinalIgnoreCase))
        {
            logger.GeographyNotAllowed(geography.Id!, account.Id!);
            return CreateBadRequest($"Geography '{geography.Id}' not supported for account '{account.Id}'.");
        }

        if (await availabilityService.ReachedInstancesLimitAsync(catalogService, accountId).ConfigureAwait(false))
        {
            return CreateBadRequest(
                $"The limit for service {serviceId} has been reached. Please contact AVEVA support to increase the limit for this account.",
                "#instanceLimitReached");
        }

        if (!availabilityService.IsEnabled(catalogService, accountId))
        {
            return CreateBadRequest($"Catalog Service '{serviceId}' not found.");
        }

        var dependencyValidationResult = await dependencyValidator.Validate(createRequest.Dependencies, catalogService, createRequest.Geography, accountId).ConfigureAwait(false);
        if (!dependencyValidationResult.IsValid)
        {
            return CreateBadRequest(dependencyValidationResult.Message!);
        }

        var labelValidationResult = labelValidator.Validate(createRequest.Labels);

        if (!labelValidationResult.IsValid) return CreateBadRequest(labelValidationResult.Message!);

        var instance = Instance.Create(
            accountId,
            catalogService.Id!,
            createRequest.Id!,
            createRequest.Geography!,
            catalogService.HostingType == HostingTypes.Regional ? geography.PrimaryRegionId : null,
            dependencyValidationResult.Dependencies,
            false,
            catalogService.Lifecycle.Protocol,
            catalogService.Lifecycle.ProviderId,
            name: createRequest.Name,
            labelIds: labelValidationResult.Labels,
            fulfillmentRequired: catalogService.Lifecycle?.FulfillmentRequired ?? false);
        
        instance.ApiModifiedDate = DateTime.UtcNow;
        instance.Applications = instanceApplicationFactory.Generate(catalogService, instance);

        var lifecycleOperationValidator = lifecycleOperationValidatorFactory.GetValidator(catalogService.Lifecycle?.Protocol?.ToString());

        if (lifecycleOperationValidator != null)
        {
            var lifecycleOperationValidation = await lifecycleOperationValidator.ValidateCreateAsync(instance).ConfigureAwait(false);
            if (!lifecycleOperationValidation.IsValid)
            {
                return CreateBadRequest(lifecycleOperationValidation.Messages.Aggregate((x, y) => $"{x}, {y}"));
            }
        }

        try
        {
            instance = await repository.SaveAsync(instance).ConfigureAwait(false);
        }
        catch (EntityAlreadyExistsException)
        {
            return Problem(
                title: CreateProblemTitle,
                detail: "An instance of the service already exists with the same Instance Id.",
                statusCode: StatusCodes.Status409Conflict);
        }

        logger.CreatedInstance(instance.InstanceId, instance.ServiceId, instance.AccountId, instance.Id!);
        logger.RecordCustomerAuditEvent(SingleVerb.Created, ApplicationConstants.AuditLogInstance, instance.InstanceId, instance.Name ?? string.Empty, instance.AccountId);
        return CreatedAtAction(nameof(GetAsync), new { serviceId = instance.ServiceId, id = instance.InstanceId }, instance.MapToResponse());
    }

    /// <summary>
    /// Update service instance
    /// </summary>
    /// <remarks>
    /// Updates an existing service instance's name, dependencies, or labels.
    /// Only active service instances can be updated.
    /// 
    /// You must have update access to the specific service instance.
    /// </remarks>
    /// <param name="serviceId">The service ID that identifies which service this instance belongs to. For a list of available service IDs, make a GET request to `/api/account/{accountId}/catalog/v2/services`.</param>
    /// <param name="id">The instance ID that uniquely identifies this specific service instance. For a list of available instance IDs, make a GET request to `/api/account/{accountId}/instanceMgmt/v1/instances`.</param>
    /// <param name="updateRequest">The properties to update, which may include name, dependencies, and labels.</param>
    /// <returns>The updated service instance details.</returns>
    [HttpPut("services/{serviceId}/instances/{id}", Name = "updateInstance")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(InstanceResponse))]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ConnectAuthorizationResponseType("instances", "update")]
    public async Task<ActionResult<InstanceResponse>> UpdateAsync([FromRoute] string serviceId, [FromRoute] string id, [FromBody] InstanceUpdateRequest? updateRequest)
    {
        if (updateRequest == null)
            return UpdateBadRequest("An update request body is required.");

        var account = Request.Headers.GetAccount();
        if (string.IsNullOrWhiteSpace(account))
            return UpdateBadRequest("Account Id is required.");

        if (string.IsNullOrWhiteSpace(serviceId))
            return UpdateBadRequest("Service Id is required.");

        if (string.IsNullOrEmpty(id))
            return UpdateBadRequest("Instance Id is required.");

        var authorizationResult = await authorization.AuthorizeResourceAsync(
            ResourcePath.Parse(AuthorizationPolicies.Resources.Instance(serviceId, id)),
            ResourceAction.Update,
            new AuthorizationMetadata(),
            CancellationToken.None).ConfigureAwait(false);

        if (!authorizationResult.IsSuccess)
        {
            logger.Forbidden();
            return Forbidden(UpdateProblemTitle, ResourceAction.Update, serviceId, id);
        }

        var instance = await repository.GetAsync(account, serviceId, id).ConfigureAwait(false);
        if (instance == null)
        {
            logger.InstanceNotFoundWithInstanceId(account, serviceId, id);
            return InstanceNotFound(UpdateProblemTitle, serviceId, id);
        }

        if (instance.Status != Domain.Instances.Status.Active)
        {
            return UpdateBadRequest("Only Active instances can be updated.");
        }

        var catalogService = await catalogClient.GetServiceAsync(serviceId).ConfigureAwait(false);
        if (catalogService == null)
        {
            logger.CatalogServiceNotFound(serviceId);
            return UpdateBadRequest($"Catalog Service '{serviceId}' not found.");
        }

        DependencyValidationResult? result = null;
        if (updateRequest.Dependencies != null)
        {
            result = await dependencyValidator.Validate(updateRequest.Dependencies, catalogService, instance.Geography, instance.AccountId, instance.Dependencies).ConfigureAwait(false);
            if (!result.IsValid)
            {
                return UpdateBadRequest(result.Message!);
            }
        }

        var labelValidationResult = labelValidator.Validate(updateRequest.Labels, instance.LabelIds);

        if (!labelValidationResult.IsValid) return UpdateBadRequest(labelValidationResult.Message!);

        var previousInstance = instance.DeepClone();

        instance.Update(
            updateRequest.Name,
            result?.Dependencies,
            labelValidationResult.Labels);
        instance.ApiModifiedDate = DateTime.UtcNow;

        var lifecycleOperationValidator = lifecycleOperationValidatorFactory.GetValidator(catalogService.Lifecycle?.Protocol?.ToString());

        if (lifecycleOperationValidator != null)
        {
            var lifecycleOperationValidation = await lifecycleOperationValidator.ValidateUpdateAsync(previousInstance, instance).ConfigureAwait(false);
            if (!lifecycleOperationValidation.IsValid)
            {
                return UpdateBadRequest(lifecycleOperationValidation.Messages.Aggregate((x, y) => $"{x}, {y}"));
            }
        }

        instance = await repository.SaveAsync(instance).ConfigureAwait(false);

        logger.UpdatedInstance(instance.InstanceId, instance.ServiceId, instance.AccountId, instance.Id!);
        logger.RecordCustomerAuditEvent(SingleVerb.Updated, ApplicationConstants.AuditLogInstance, instance.InstanceId, instance.Name ?? string.Empty, instance.AccountId);
        return Ok(instance.MapToResponse());
    }

    /// <summary>
    /// Delete service instance
    /// </summary>
    /// <remarks>
    /// Deletes a service instance by its service ID and instance ID.
    /// Deleted service instances remain in the account for 30 days, after which they are permanently removed.
    /// To recover a deleted service instance during this period, contact AVEVA Support.
    /// 
    /// You must have delete access to the specific service instance.
    /// </remarks>
    /// <param name="serviceId">The service ID that identifies which service this instance belongs to. For a list of available service IDs, make a GET request to `/api/account/{accountId}/catalog/v2/services`.</param>
    /// <param name="id">The instance ID that uniquely identifies this specific service instance. For a list of available instance IDs, make a GET request to `/api/account/{accountId}/instanceMgmt/v1/instances`.</param>
    /// <returns>The deleted service instance details.</returns>
    [HttpDelete("services/{serviceId}/instances/{id}", Name = "deleteInstance")]
    [ProducesResponseType(StatusCodes.Status202Accepted, Type = typeof(InstanceResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status408RequestTimeout)]
    [ConnectAuthorizationResponseType("instances", "delete")]
    public async Task<ActionResult<InstanceResponse>> DeleteAsync([FromRoute] string serviceId, [FromRoute] string id)
    {
        var account = Request.Headers.GetAccount();
        if (string.IsNullOrWhiteSpace(account))
            return DeleteBadRequest("Account Id is required.");

        if (string.IsNullOrWhiteSpace(serviceId))
            return DeleteBadRequest("Service Id is required.");

        if (string.IsNullOrWhiteSpace(id))
            return DeleteBadRequest("Instance Id is required.");

        var authorizationResult = await authorization.AuthorizeResourceAsync(
            ResourcePath.Parse(AuthorizationPolicies.Resources.Instance(serviceId, id)),
            ResourceAction.Delete,
            new AuthorizationMetadata(),
            CancellationToken.None).ConfigureAwait(false);

        if (!authorizationResult.IsSuccess)
        {
            logger.Forbidden();
            return Forbidden(DeleteProblemTitle, ResourceAction.Delete, serviceId, id);
        }

        var instance = await repository.GetAsync(account, serviceId, id).ConfigureAwait(false);
        if (instance == null)
        {
            logger.InstanceNotFoundApi(account, serviceId, id);
            return InstanceNotFound(DeleteProblemTitle, serviceId, id);
        }

        var validationResult = await instanceValidator.ValidateDeleteAsync(instance, Scope.Api).ConfigureAwait(false);

        if (!validationResult.IsValid)
        {
            return DeleteBadRequest(validationResult.Message!);
        }

        try
        {
            instance.SoftDelete(false);
        }
        catch (StatusChangeException ex)
        {
            return DeleteBadRequest(ex.Message);
        }

        instance.ApiModifiedDate = DateTime.UtcNow;
        instance = await repository.SaveAsync(instance).ConfigureAwait(false);

        logger.DeletingInstance(account, serviceId, id);
        logger.RecordCustomerAuditEvent(SingleVerb.Deleted, ApplicationConstants.AuditLogInstance, instance.InstanceId, instance.Name ?? string.Empty, instance.AccountId);
        return AcceptedAtAction(nameof(GetAsync), new { serviceId = instance.ServiceId, id = instance.InstanceId }, instance.MapToResponse());
    }

    private static bool ExternalGeographySupported(ServiceResponse catalogService, string? id)
        => catalogService.Geographies?.Any(x => x.Id != null && x.Id.Equals(id, StringComparison.OrdinalIgnoreCase)) ?? false;

    private async Task<List<string>> GetServicesInCategoryAsync(Models.Common.v1.Category? category)
    {
        return (await catalogClient.GetServicesAsync(category.MapToService()).ConfigureAwait(false))
            .Select(x => x.Id)
            .Where(x => x != null)
            .Cast<string>()
            .ToList();
    }

    private async Task<List<string>> GetFilteredManagedServicesAsync(bool userManaged)
    {
        return (await catalogClient.GetServicesAsync().ConfigureAwait(false))
            .Where(x => x.Lifecycle != null && x.Lifecycle.Trigger != null)
            .Where(x => userManaged == x.Lifecycle!.Trigger!.Equals(LifecycleTriggers.Catalog))
            .Select(x => x.Id)
            .Where(x => x != null)
            .Cast<string>()
            .ToList();
    }

    private OkObjectResult EmptyInstanceCollectionResponse(Paging paging)
    {
        return paging switch
        {
            OffsetPaging => Ok(new InstanceCollectionResponse { Items = [], TotalCount = 0 }),
                _ => Ok(new InstanceCollectionResponse { Items = [] }),
        };
    }

    private ObjectResult QueryBadRequest(string detail) => Problem(
        title: QueryProblemTitle,
        detail: detail,
        statusCode: StatusCodes.Status400BadRequest);

    private ObjectResult GetBadRequest(string detail) => Problem(
        title: GetProblemTitle,
        detail: detail,
        statusCode: StatusCodes.Status400BadRequest);

    private ObjectResult CreateBadRequest(string detail) => Problem(
        title: CreateProblemTitle,
        detail: detail,
        statusCode: StatusCodes.Status400BadRequest);

    private ObjectResult CreateBadRequest(string detail, string errorId)
    {
        return Problem(
            title: CreateProblemTitle,
            detail: detail,
            statusCode: StatusCodes.Status400BadRequest,
            type: Path.Combine(ErrorConstants.AvevaErrorHelpPage, errorId));
    }

    private ObjectResult UpdateBadRequest(string detail) => Problem(
        title: UpdateProblemTitle,
        detail: detail,
        statusCode: StatusCodes.Status400BadRequest);

    private ObjectResult DeleteBadRequest(string detail) => Problem(
        title: DeleteProblemTitle,
        detail: detail,
        statusCode: StatusCodes.Status400BadRequest);

    private ObjectResult InstanceNotFound(string problemTitle, string serviceId, string id) => Problem(
        title: problemTitle,
        detail: $"Instance with service id {serviceId} and instance id {id} cannot be found.",
        statusCode: StatusCodes.Status404NotFound);

    private ObjectResult Forbidden(string problemTitle, string action, string? serviceId = null, string? id = null) => Problem(
        title: problemTitle,
        detail: $"Forbidden to {action} {(serviceId != null ? $"an instance with service id {serviceId}" : "instances")}{(id != null ? $" and instance id {id}" : string.Empty)}.",
        statusCode: StatusCodes.Status403Forbidden);
}
